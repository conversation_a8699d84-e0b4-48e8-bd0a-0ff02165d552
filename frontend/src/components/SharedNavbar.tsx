import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

interface SharedNavbarProps {
  variant?: 'homepage' | 'plaza';
}

const SharedNavbar: React.FC<SharedNavbarProps> = ({ variant = 'homepage' }) => {
  useEffect(() => {
    if (variant === 'homepage') {
      // 导航栏滚动效果（仅在首页）
      const header = document.querySelector('header');

      const handleScroll = () => {
        if (window.scrollY > 50) {
          header?.classList.add('scrolled');
        } else {
          header?.classList.remove('scrolled');
        }
      };

      window.addEventListener('scroll', handleScroll);

      // 移动端菜单切换
      const menuToggle = document.querySelector('.menu-toggle');
      const navLinks = document.querySelector('.nav-links');

      const handleMenuToggle = () => {
        navLinks?.classList.toggle('active');
      };

      menuToggle?.addEventListener('click', handleMenuToggle);

      // 清理事件监听器
      return () => {
        window.removeEventListener('scroll', handleScroll);
        menuToggle?.removeEventListener('click', handleMenuToggle);
      };
    }
  }, [variant]);

  if (variant === 'plaza') {
    // 项目广场页面的导航栏（使用首页样式但适配项目广场）
    return (
      <header className="plaza-header">
        <nav className="container">
          <div className="logo">
            <Link to="/" className="logo-link">
              <img src="/logo.png" alt="P-Box Logo" />
              <span>P-Box</span>
            </Link>
          </div>
          <div className="nav-links">
            <Link to="/">首页</Link>
            <Link to="/plaza">项目广场</Link>
            <Link to="/login" className="nav-admin-link">登录</Link>
          </div>
          <div className="menu-toggle">
            <i className="fas fa-bars"></i>
          </div>
        </nav>
      </header>
    );
  }

  // 首页的导航栏
  return (
    <header>
      <nav className="container">
        <div className="logo">
          <img src="/logo.png" alt="P-Box Logo" />
          <span>P-Box</span>
        </div>
        <div className="nav-links">
          <a href="#features">功能特点</a>
          <a href="#screenshots">界面展示</a>
          <a href="#download">立即下载</a>
          <a href="#contact">联系我们</a>
          <Link to="/plaza">项目广场</Link>
          <Link to="/login" className="nav-admin-link">登录</Link>
        </div>
        <div className="menu-toggle">
          <i className="fas fa-bars"></i>
        </div>
      </nav>
    </header>
  );
};

export default SharedNavbar;
