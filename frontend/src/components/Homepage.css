/* Homepage 组件样式 */
.homepage {
  /* 基础样式变量 */
  --primary-color: #396cd8;
  --primary-dark: #2a5bb9;
  --secondary-color: #4CAF50;
  --text-color: #333;
  --text-light: #666;
  --background-color: #f6f6f6;
  --white: #ffffff;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

.homepage * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.homepage {
  font-family: 'Inter', 'Avenir', 'Helvetica', 'Arial', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--background-color);
}

.homepage .container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.homepage section {
  padding: 80px 0;
}

.homepage .section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.homepage .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* 导航栏样式 */
.homepage header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: var(--transition);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.homepage header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow);
}

.homepage nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.homepage .logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.homepage .logo img {
  width: 40px;
  height: 40px;
}

.homepage .nav-links {
  display: flex;
  gap: 30px;
  align-items: center;
}

.homepage .nav-links a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.homepage .nav-links a:hover {
  color: var(--primary-color);
}

.homepage .nav-links a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.homepage .nav-links a:hover::after {
  width: 100%;
}

.homepage .nav-admin-link {
  background: var(--primary-color);
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: var(--transition);
}

.homepage .nav-admin-link:hover {
  background: var(--primary-dark);
  color: white !important;
}

.homepage .nav-admin-link::after {
  display: none;
}

.homepage .menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--primary-color);
}

/* 项目广场页面导航栏样式 */
.plaza-header {
  position: static;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.plaza-header nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.plaza-header .logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.plaza-header .logo-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: var(--primary-color);
}

.plaza-header .logo img {
  width: 40px;
  height: 40px;
}

.plaza-header .nav-links {
  display: flex;
  gap: 30px;
  align-items: center;
}

.plaza-header .nav-links a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.plaza-header .nav-links a:hover {
  color: var(--primary-color);
}

.plaza-header .nav-links a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.plaza-header .nav-links a:hover::after {
  width: 100%;
}

.plaza-header .nav-admin-link {
  background: var(--primary-color);
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: var(--transition);
}

.plaza-header .nav-admin-link:hover {
  background: var(--primary-dark);
  color: white !important;
}

.plaza-header .nav-admin-link::after {
  display: none;
}

.plaza-header .menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--primary-color);
}

/* 英雄区域样式 */
.homepage .hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 0 80px;
  margin-top: 70px;
}

.homepage .hero .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.homepage .hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  line-height: 1.2;
}

.homepage .hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.homepage .hero-buttons {
  display: flex;
  gap: 20px;
}

.homepage .btn {
  display: inline-block;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  border: 2px solid transparent;
}

.homepage .primary-btn {
  background: var(--white);
  color: var(--primary-color);
}

.homepage .primary-btn:hover {
  background: transparent;
  color: var(--white);
  border-color: var(--white);
}

.homepage .secondary-btn {
  background: transparent;
  color: var(--white);
  border-color: var(--white);
}

.homepage .secondary-btn:hover {
  background: var(--white);
  color: var(--primary-color);
}

.homepage .hero-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 功能特点样式 */
.homepage .features {
  background: var(--white);
}

.homepage .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.homepage .feature-card {
  background: var(--white);
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: var(--shadow);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.homepage .feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.homepage .feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.homepage .feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--text-color);
}

.homepage .feature-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* 界面展示样式 */
.homepage .screenshots {
  background: var(--background-color);
}

.homepage .screenshot-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.homepage .screenshot-slider {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.homepage .screenshot {
  display: none;
  text-align: center;
  background: var(--white);
  padding: 20px;
}

.homepage .screenshot.active {
  display: block;
}

.homepage .screenshot img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 15px;
}

.homepage .screenshot p {
  color: var(--text-light);
  font-size: 1.1rem;
}

.homepage .slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--primary-color);
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.homepage .slider-btn:hover {
  background: var(--white);
  transform: translateY(-50%) scale(1.1);
}

.homepage .prev-btn {
  left: -25px;
}

.homepage .next-btn {
  right: -25px;
}

/* 下载区域样式 */
.homepage .download {
  background: var(--white);
}

.homepage .download-options {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.homepage .download-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 40px 60px;
  background: var(--white);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.homepage .download-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.homepage .download-btn i {
  font-size: 3rem;
}

.homepage .download-btn span {
  font-size: 1.2rem;
  font-weight: 600;
}

/* 联系我们样式 */
.homepage .contact {
  background: var(--background-color);
}

.homepage .contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.homepage .contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.homepage .contact-item i {
  font-size: 2rem;
  color: var(--secondary-color);
}

.homepage .contact-item p {
  font-size: 1.2rem;
  font-weight: 600;
}

.homepage .contact-description {
  color: var(--text-light);
  line-height: 1.6;
}

.homepage .qr-code {
  text-align: center;
}

.homepage .qr-placeholder {
  width: 200px;
  height: 200px;
  background: var(--white);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: var(--shadow);
}

.homepage .qr-placeholder img {
  width: 180px;
  height: 180px;
  border-radius: 8px;
}

/* 页脚样式 */
.homepage footer {
  background: var(--text-color);
  color: var(--white);
  padding: 40px 0;
}

.homepage .footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.homepage .footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: bold;
}

.homepage .footer-logo img {
  width: 30px;
  height: 30px;
}

.homepage .copyright {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
}

.homepage .copyright:hover {
  color: var(--white);
}

/* 返回顶部按钮 */
.homepage .back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
}

.homepage .back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.homepage .back-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .homepage .nav-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--white);
    flex-direction: column;
    padding: 20px;
    box-shadow: var(--shadow);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }

  .homepage .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .homepage .menu-toggle {
    display: block;
  }

  /* 项目广场页面响应式样式 */
  .plaza-header .nav-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--white);
    flex-direction: column;
    padding: 20px;
    box-shadow: var(--shadow);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }

  .plaza-header .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .plaza-header .menu-toggle {
    display: block;
  }

  .homepage .hero .container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .homepage .hero-content h1 {
    font-size: 2.5rem;
  }

  .homepage .hero-buttons {
    justify-content: center;
  }

  .homepage .features-grid {
    grid-template-columns: 1fr;
  }

  .homepage .download-options {
    flex-direction: column;
    align-items: center;
  }

  .homepage .contact-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .homepage .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .homepage .slider-btn {
    display: none;
  }
}
